import express from 'express';
import dotenv from 'dotenv';
import morgan from 'morgan';
import compression from 'compression';

// Load environment variables
dotenv.config();

import connectDB from './config/database';
import { errorHandler, notFound } from './middleware/errorHandler';
import { generalLimiter, corsOptions, helmetConfig } from './middleware/security';
import cors from 'cors';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/user';

const app = express();
const PORT = process.env.PORT || 3000;

// Connect to database
connectDB();

// Security middleware
app.use(helmetConfig);
app.use(cors(corsOptions));
app.use(generalLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined'));
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);

// API documentation endpoint
app.get('/api', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Wasel API v1.0.0',
    endpoints: {
      auth: {
        'POST /api/auth/signup': 'Create a new user account',
        'POST /api/auth/login': 'Login with email and password',
        'POST /api/auth/refresh-token': 'Refresh access token',
        'POST /api/auth/forgot-password': 'Request password reset',
        'POST /api/auth/reset-password': 'Reset password with token',
        'POST /api/auth/logout': 'Logout (requires auth)',
        'POST /api/auth/logout-all': 'Logout from all devices (requires auth)',
      },
      users: {
        'GET /api/users/profile': 'Get current user profile (requires auth)',
        'PUT /api/users/profile': 'Update user profile (requires auth)',
        'PUT /api/users/change-password': 'Change password (requires auth)',
        'DELETE /api/users/account': 'Deactivate account (requires auth)',
        'GET /api/users/:userId': 'Get user by ID (requires auth)',
      },
    },
  });
});

// 404 handler
app.use(notFound);

// Error handling middleware
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`
🚀 Server is running on port ${PORT}
📝 Environment: ${process.env.NODE_ENV}
🌐 API Documentation: http://localhost:${PORT}/api
❤️  Health Check: http://localhost:${PORT}/health
  `);
});

export default app;
