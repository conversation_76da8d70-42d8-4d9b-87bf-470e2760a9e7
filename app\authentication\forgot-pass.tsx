import { Stack } from 'expo-router';

import { Container } from '~/components/Container';
import { ScreenContent } from '~/components/authentication-components/ScreenContent';

export default function ForgotPass() {

  return (
    <>
      <Stack.Screen options={{ title: 'ForgotPass' }} />
      <Container alignSelf='center' alignItems="center" justifyContent="center" paddingTop={85} paddingBottom={85} style={{ width: '90%' }}>
        <ScreenContent path="app/authentication/forgot-pass.tsx" title={`ForgotPass`} />
      </Container>
    </>
  );
}