import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'tamagu<PERSON>';
import { CustomTextField } from '../CustomTextField';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form'
import { Button } from '~/components/Button';
import { Link, useRouter } from 'expo-router';
import <PERSON>gin<PERSON>ogo from '../LoginLogo';
import { Alert } from 'react-native';
import { useCurrentUserData } from '../useCurrentUserData';
import { apiService } from '../../services/api';
import { useState } from 'react';

type LoginGUIProps = {
    children?: React.ReactNode;
    methods: UseFormReturn<FieldValues, any, FieldValues>;
}

export const LoginGUI = ({ children, methods }: LoginGUIProps) => {
    const router = useRouter();
    const { handleSubmit, getValues } = methods;
    const { setCurrentUser, setLoading, setError } = useCurrentUserData();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const onSubmit = async () => {
      const { ['username or email']: email, password } = getValues();

      if (!email || !password) {
        Alert.alert('Login Failed', 'Please enter both email and password.');
        return;
      }

      setIsSubmitting(true);
      setLoading(true);
      setError(null);

      try {
        const response = await apiService.login({
          email: email.trim().toLowerCase(),
          password,
        });

        if (response.success && response.data) {
          setCurrentUser(response.data.user);

          // Redirect based on role
          if (response.data.user.role === 'customer') {
            router.push('/(customer-pages)/home');
          } else if (response.data.user.role === 'supplier') {
            router.push('/(supplier-pages)/home');
          }
        } else {
          Alert.alert('Login Failed', response.message || 'Invalid email or password.');
          setError(response.message || 'Login failed');
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Network error occurred';
        Alert.alert('Login Failed', errorMessage);
        setError(errorMessage);
      } finally {
        setIsSubmitting(false);
        setLoading(false);
      }
    };


    return (
        <Theme name="light">
              <FormProvider {...methods}>
                <YStack flex={1} alignItems="center" justifyContent="center" gap="$4">
                  <Separator />
                  <LoginLogo />
                  <CustomTextField name="username or email" icon="person" label="UserName or Email" placeholder="Enter your username or email" />
                  <CustomTextField name="password" icon="lock-closed" label="Password" secureTextEntry placeholder="Enter your password" />
                  <Link style={{color: "#0063FF",  textDecorationLine: 'underline', padding: 8}} href={{ pathname: '/authentication/verify-before-reset1', params: { name: 'Dan' } }} asChild>
                      <H6>Forgot Password? Click Here</H6>
                  </Link>
                  <XStack justifyContent="center" alignItems="center" gap="$2" padding={"2%"} width={'100%'}>
                    <Button
                      width="45%"
                      title={isSubmitting ? "Logging in..." : "Login"}
                      onPress={handleSubmit(onSubmit)}
                      disabled={isSubmitting}
                    />
                    <Link href={{ pathname: '/authentication/signup', params: { name: 'Dan' } }} asChild>
                      <Button width={'45%'} title="Signup" style={{ backgroundColor: '#67B329', color:"#FFFFFF" }} />
                    </Link>
                  </XStack>
                  {children}
                </YStack>
                
              </FormProvider>
        </Theme>
    );
};