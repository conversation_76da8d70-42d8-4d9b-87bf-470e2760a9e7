import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, H<PERSON>, <PERSON>ara<PERSON>, Theme } from 'tamagui';
import { CustomTextField } from '../CustomTextField';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form'
import { Button } from '~/components/Button';
import { Link, useRouter } from 'expo-router';
import <PERSON>gin<PERSON>ogo from '../LoginLogo';
import { Alert } from 'react-native';
import { users } from '~/temp-data/users';
import { useCurrentUserData } from '../useCurrentUserData';

type LoginGUIProps = {
    children?: React.ReactNode;
    methods: UseFormReturn<FieldValues, any, FieldValues>;
}

export const LoginGUI = ({ children, methods }: LoginGUIProps) => {
    const router = useRouter();
    const { handleSubmit, getValues } = methods;
    const setCurrentUser = useCurrentUserData((s) => s.setCurrentUser);

    const onSubmit = () => {
      const { ['username or email']: email, password } = getValues();

      const user = users.find(
        (u) => u.email === email.trim().toLowerCase() && u.password === password
      );

      if (!user) {
        Alert.alert('Login Failed', 'Invalid email or password.');
        return;
      }

      setCurrentUser(user);

      // Redirect based on role
      if (user.role === 'customer') {
        router.push('/(customer-pages)/home');
      } else if (user.role === 'supplier') {
        router.push('/(supplier-pages)/home');
      }
    };


    return (
        <Theme name="light">
              <FormProvider {...methods}>
                <YStack flex={1} alignItems="center" justifyContent="center" gap="$4">
                  <Separator />
                  <LoginLogo />
                  <CustomTextField name="username or email" icon="person" label="UserName or Email" placeholder="Enter your username or email" />
                  <CustomTextField name="password" icon="lock-closed" label="Password" secureTextEntry placeholder="Enter your password" />
                  <Link style={{color: "#0063FF",  textDecorationLine: 'underline', padding: 8}} href={{ pathname: '/authentication/verify-before-reset1', params: { name: 'Dan' } }} asChild>
                      <H6>Forgot Password? Click Here</H6>
                  </Link>
                  <XStack justifyContent="center" alignItems="center" gap="$2" padding={"2%"} width={'100%'}>
                    <Button width="45%" title="Login" onPress={handleSubmit(onSubmit)} />
                    <Link href={{ pathname: '/authentication/signup', params: { name: 'Dan' } }} asChild>
                      <Button width={'45%'} title="Signup" style={{ backgroundColor: '#67B329', color:"#FFFFFF" }} />
                    </Link>
                  </XStack>
                  {children}
                </YStack>
                
              </FormProvider>
        </Theme>
    );
};