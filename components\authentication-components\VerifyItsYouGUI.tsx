import { <PERSON><PERSON><PERSON><PERSON>, Separator, Theme, H6 } from 'tamagui';
import { CustomTextField } from '../CustomTextField';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form'
import { Button } from '~/components/Button';
import { Link } from 'expo-router';
import { Image } from 'react-native';

type VerifyItsYouProps = {
    children?: React.ReactNode;
    methods: UseFormReturn<FieldValues, any, FieldValues>;
    onSubmit: () => void;
    verifyCodePage: boolean;
}

export const VerifyItsYou = ({ children, methods, onSubmit, verifyCodePage }: VerifyItsYouProps) => {
    return (
        <Theme name="light">
            <FormProvider {...methods}>
                <YStack flex={1} alignItems="center" justifyContent="center" gap="$4">
                    <Separator />
                    <Image
                        source={require('../../assets/forgot.png')}
                        style={{ width: 128, height: 128, padding: 8 }}
                    />
                    <CustomTextField name={verifyCodePage ? "verification code" : "email"} icon={verifyCodePage ? "keypad" : "mail"} keyboardType={verifyCodePage ? "phone-pad" : "email-address"} label={verifyCodePage ? "Verification Code" : "Email"} placeholder={verifyCodePage ? "Enter the verification code" : "Enter your email"} />
                    { verifyCodePage ? (
                    <Link style={{color: "#0063FF",  textDecorationLine: 'underline', padding: 8}} href={{ pathname: '/authentication/verify-before-reset1', params: { name: 'Dan' } }} asChild>
                        <H6>Resend Code</H6>
                    </Link>
                    ) : null
                    }
                    <Link href={{ pathname: verifyCodePage ? '/authentication/forgot-pass' : '/authentication/verify-before-reset2', params: { name: 'Dan' } }} asChild>
                        <Button width={'45%'} margin={'$2'} title={verifyCodePage ? "Verify" : "Send Code"} /*onPress={onSubmit} - for future (link removed in future)*/></Button>
                    </Link>
                    {children}
                </YStack>
                
            </FormProvider>
        </Theme>
    );
};