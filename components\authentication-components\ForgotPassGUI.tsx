import { YS<PERSON><PERSON>, Separator, Theme } from 'tamagui';
import { CustomTextField } from '../CustomTextField';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form'
import { Button } from '~/components/Button';
import { Avatar, AvatarImage, AvatarFallback } from '@tamagui/avatar';
import { Ionicons } from '@expo/vector-icons';

type ForgotPassProps = {
    children?: React.ReactNode;
    methods: UseFormReturn<FieldValues, any, FieldValues>;
    onSubmit: () => void;
}

export const ForgotPass = ({ children, methods, onSubmit }: ForgotPassProps) => {
    return (
        <Theme name="light">
            <FormProvider {...methods}>
                <YStack flex={1} alignItems="center" justifyContent="center" gap="$4">
                <Separator />
                <Avatar circular size="$12" borderWidth={2} borderColor="$gray6">
                    <AvatarImage src="https://example.com/profile.jpg" />
                    <AvatarFallback bg="$gray6" alignItems="center" justifyContent="center">
                    <Ionicons name="person" size={64} color="#FFFFFF" />
                    </AvatarFallback>
                </Avatar>
                <CustomTextField name="new password" icon="lock-closed" label="New Password" secureTextEntry placeholder="Enter a new password" />
                <CustomTextField name="confirm new password" icon="lock-closed" label="Confirm New Password" secureTextEntry placeholder="Confirm the new password" />
                <Button width={'45%'} title="Reset" onPress={onSubmit}></Button>
                {children}
                </YStack>
            </FormProvider>
        </Theme>
    );
};