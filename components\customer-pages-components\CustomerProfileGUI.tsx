import { Card, YStack, Text, View, H2, XStack, Paragraph } from 'tamagui';
import { Avatar, AvatarImage, AvatarFallback } from '@tamagui/avatar'
import { Ionicons } from '@expo/vector-icons'
import { ImageBackground, Pressable, View as RNView, ScrollView } from 'react-native';
import { router } from 'expo-router';

type CustomerProfileGUIProps = {
  title: string;
};

export const CustomerProfileGUI = ({ title }: CustomerProfileGUIProps) => {
    const AVATAR_SIZE = 128;
    const CUTOUT_RADIUS = AVATAR_SIZE / 2 + 5;
    return (
        <YStack flex={1} position="relative" backgroundColor="#fff" width={'100%'} height={'100%'}>
            <YStack position="absolute" top={50} zIndex={1} width="100%" alignItems="center" justifyContent='center'>
                <H2 style={{color: 'white', fontWeight: 'bold'}}>Profile</H2>
            </YStack>
            {/* ✅ Wrapper with rounded corners */}
            <RNView
                style={{
                height: 250,
                width: '100%',
                borderBottomLeftRadius: 20,
                borderBottomRightRadius: 20,
                overflow: 'hidden',
                position: 'absolute',
                top: 0,
                }}
            >
            {/* Primary background + cutout */}
            <ImageBackground
                source={require('../../assets/profile-bg.png')} // adjust path as needed
                resizeMode="cover"
                style={{
                height: 250,
                width: '100%',
                position: 'absolute',
                top: 0,
                zIndex: 0,
                justifyContent: 'flex-end',
                alignItems: 'center'
                }}
            >
                {/* White cutout circle */}
                <View
                width={CUTOUT_RADIUS * 2}
                height={CUTOUT_RADIUS * 2}
                borderRadius={CUTOUT_RADIUS}
                backgroundColor="#fff"
                position="absolute"
                bottom={-CUTOUT_RADIUS}
                />
            </ImageBackground>
            </RNView>

            {/* Avatar overlapping the cutout */}
            <YStack alignItems="center" marginTop={185} zIndex={3}>
                <View position="relative" width={AVATAR_SIZE} height={AVATAR_SIZE}>
                    <Avatar circular size={AVATAR_SIZE} borderWidth={2} borderColor="$gray6">
                    <AvatarImage src="https://example.com/profile.jpg" />
                    <AvatarFallback bg="$gray6" alignItems="center" justifyContent="center">
                        <Ionicons name="person" size={64} color="#FFFFFF" />
                    </AvatarFallback>
                    </Avatar>
                    {/* Camera Icon Button */}
                    <Pressable
                        onPress={() => {
                            console.log('Camera button pressed');
                            // Trigger image picker logic here
                        }}
                        style={({ pressed, hovered }) => ({
                            position: 'absolute',
                            bottom: 0,
                            right: 0,
                            backgroundColor: pressed
                                ? '#4c1473' // darker when pressed
                                : hovered
                                ? '#5e1c93' // slightly darker when hovered (web only)
                                : '#7529B3', // default
                            borderRadius: 20,
                            padding: 6,
                            borderWidth: 2,
                            borderColor: '#fff'
                        })}
                    >
                        <Ionicons name="camera" size={20} color="#fff" />
                    </Pressable>
                </View>
                <Text fontSize="$7" fontWeight="700" marginTop="$4">
                {title}
                </Text>
                <Text color='$gray9' padding={'$1'}><EMAIL></Text>
                <Text fontSize="$5" fontWeight="700" padding={'$1'}>+970591234567</Text>
                <View
                    height={4} // thicker line
                    width="80%"
                    backgroundColor="#67B329"
                    borderRadius={9999} // max rounded edges
                    alignSelf="center"
                    marginVertical="$4"
                />
            </YStack>
                <ScrollView contentContainerStyle={{ flexGrow: 1, width: '95%', paddingBottom: 110, alignContent: 'center', alignSelf: 'center', rowGap:5 }} showsVerticalScrollIndicator={false} style={{ borderBottomColor: '#7529B3', borderBottomWidth: 3, borderCurve: 'circular', borderRadius: 10 }}>
                    {/* Phone Number Card */}
                    <Pressable 
                        onPress={() => console.log("Edit Phone Number")}
                        style={({ pressed, hovered }) => ({
                            backgroundColor: pressed ? '#67B329' : hovered ? '#67B329' : '#fff',
                            borderRadius: 10,
                            padding: 2
                        })}
                    >
                        <Card justifyContent='center' height='$6'  padding="$3" margin="$0" elevate asChild>
                            <XStack alignItems="center" gap="$3">
                                <Ionicons name="call-outline" size={20} color="#7529B3" />
                                <Paragraph fontSize='$5' fontWeight="600">Edit Phone Number</Paragraph>
                            </XStack>
                        </Card>
                    </Pressable>

                    {/* Address Card */}
                    <Pressable 
                        onPress={() => console.log("Edit Address")}
                        style={({ pressed, hovered }) => ({
                            backgroundColor: pressed ? '#67B329' : hovered ? '#67B329' : '#fff',
                            borderRadius: 10,
                            padding: 2,
                        })}
                    >
                        <Card justifyContent='center' height='$6'  padding="$3" margin="$0" elevate asChild>
                            <XStack alignItems="center" gap="$3">
                                <Ionicons name="location-outline" size={20} color="#7529B3" />
                                <Paragraph fontSize='$5' fontWeight="600">Edit Address</Paragraph>
                            </XStack>
                        </Card>
                    </Pressable>

                    {/* Password Card */}
                    <Pressable 
                        onPress={() => console.log("Edit Password")}
                        style={({ pressed, hovered }) => ({
                            backgroundColor: pressed ? '#67B329' : hovered ? '#67B329' : '#fff',
                            borderRadius: 10,
                            padding: 2,
                        })}
                    >
                        <Card justifyContent='center' height='$6' padding="$3" margin="$0" elevate asChild>
                            
                            <XStack alignItems="center" gap="$3">
                                <Ionicons name="lock-closed-outline" size={20} color="#7529B3" />
                                <Paragraph fontSize='$5' fontWeight="600">Edit Password</Paragraph>
                            </XStack>
                            
                        </Card>
                    </Pressable>

                    {/* Logout Card */}
                    <Pressable 
                        onPress={() => {
                            router.replace('/');
                        }}
                        style={({ pressed, hovered }) => ({
                            backgroundColor: pressed ? '#DB0000' : hovered ? 'lightgray' : '#fff',
                            borderRadius: 10,
                            padding: 2,
                        })}
                    >
                        <Card justifyContent='center' height='$6' padding="$3" margin="$0" elevate asChild>
                            
                            <XStack alignItems="center" gap="$3">
                                <Ionicons name="exit-outline" size={20} color="#DB0000" />
                                <Paragraph color="#DB0000" fontSize='$5' fontWeight="600">Logout</Paragraph>
                            </XStack>
                            
                        </Card>
                    </Pressable>
                </ScrollView>

        </YStack>
    );
};