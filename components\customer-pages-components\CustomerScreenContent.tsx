import React from 'react';
import { useLocalSearchParams } from 'expo-router';
import { useState, useMemo } from 'react';
import { promotions } from '../../temp-data/promotions';
import { suppliersData } from '../../temp-data/suppliersData';
import { CustomerProfileGUI } from './CustomerProfileGUI';
import { CustomerHomeGUI } from './CustomerHomeGUI';
import { SupplierCategoriesGUI } from './SupplierCategoriesGUI';
import { SuppliersPageGUI } from './SuppliersPageGUI';
import { SupplierDetailsGUI } from './SupplierDetailsGUI';
import { SupplierProductDetailsGUI } from './SupplierProductDetailsGUI';
import { CheckoutPage } from './OrderCheckout';
import { OrderConfirmation } from './OrderConfirmation';
import { CustomerOrdersGUI } from './orders-page-components/CustomerOrdersGUI';
import { OrderDetails } from './orders-page-components/OrderDetails';
import { OrderTracking } from './orders-page-components/OrderTracking';
import { SendPackageForm } from './package-transformation/SendPackageForm';
import { SendPackageConfirmation } from './package-transformation/SendPackageConfirmation';
import { SelectLocation } from './package-transformation/SelectLocation';
import { RequestPickupForm } from './package-transformation/RequestPickupForm';
import { RequestPickupConfirmation}  from './package-transformation/RequestPickupConfirmation';
import { CustomerPackagesGUI } from './package-transformation/CustomerPackagesGUI';
import { PackageTracking } from './package-transformation/PackageTracking';
import SuppliersMap from './SuppliersMap';

type CustomerScreenContentProps = {
  title: string;
  path: string;
  children?: React.ReactNode;
};

export const CustoemrScreenContent = ({ title, path, children }: CustomerScreenContentProps) => {
  const { category } = useLocalSearchParams<{ category: string }>();
  const { supplierId } = useLocalSearchParams<{ supplierId: string }>();
  const [search, setSearch] = useState('');
  const currentSupplier = suppliersData.find(s => s.id === supplierId) || suppliersData[0];

  const filteredPromotions = useMemo(() => {
    return promotions.filter(p => p.category === category && p.title.toLowerCase().includes(search.toLowerCase()));
  }, promotions);

  const [selectedCategory, setSelectedCategory] = useState('All');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Type guard for products with category property
  function hasCategory(p: any): p is { category: string; name: string } {
    return typeof p.category === 'string' && typeof p.name === 'string';
  }

  const filteredProducts = useMemo(() => {
    let products = currentSupplier.products.filter(hasCategory);
    if (selectedCategory !== 'All') {
      products = products.filter(p => p.category === selectedCategory);
    }
    if (searchQuery) {
      products = products.filter(p => p.name.toLowerCase().includes(searchQuery.toLowerCase()));
    }
    return products;
  }, [currentSupplier.products, selectedCategory, searchQuery]);

  const supplierPromotions = suppliersData
    .find(s => s.id === supplierId)
    ?.products.filter(p => typeof p.discountPrice === 'number' && p.discountPrice > 0);

  // extract categories from currentSupplier->products
  const supplierCategories = useMemo(() => {
    const allCategories = currentSupplier.products.map(p => p.category);
    return ['All', ...new Set(allCategories)];
  }, [currentSupplier.products]);

  const filteredSuppliers = suppliersData
    .filter(s => s.category === category)
    .filter(s => s.name.toLowerCase().includes(search.toLowerCase()));

  return ((title==="CustomerProfile") ? (
      <CustomerProfileGUI title={title} />
    ) : (title==="SuppliersMap") ? (
      <SuppliersMap />
    ) : (title==="CustomerOrders") ? (
      <CustomerOrdersGUI />
    ) : (title==="CustomerPackages") ? (
      <CustomerPackagesGUI />
    ) : (title==="OrderDetails") ? (
      <OrderDetails />
    ) : (title==="OrderTracking") ? (
      <OrderTracking />
    ) : (title==="CustomerHome") ? (
      <CustomerHomeGUI />
    ) : (title==="SupplierCategories") ? (
      <SupplierCategoriesGUI />
    ) : (title==="SuppliersPage") ? (
      <SuppliersPageGUI 
        category={category} 
        search={search} 
        setSearch={setSearch} 
        filteredSuppliers={filteredSuppliers} 
        filteredPromotions={filteredPromotions} 
      />
    ) : (title==="SupplierDetails") ? (
      <SupplierDetailsGUI 
        currentSupplier={currentSupplier} 
        supplierPromotions={supplierPromotions ?? []} 
        setShowFilters={setShowFilters} 
        supplierCategories={supplierCategories} 
        selectedCategory={selectedCategory} 
        setSelectedCategory={setSelectedCategory} 
        filteredProducts={filteredProducts} 
      />
    ) : (title==="SupplierProductDetails") ? (
      <SupplierProductDetailsGUI 
        category={category || ''}
      />
    ) : (title==="OrderCheckout") ? (
      <CheckoutPage category={category || ''}/>
    ) : (title==="OrderConfirmation") ? (
      <OrderConfirmation />
    ) : (title==="SendPackage") ? (
      <SendPackageForm />
    ) : (title==="SendPackageConfirmation") ? (
      <SendPackageConfirmation />
      
    ) : (title==="RequestPickup") ? (
      <RequestPickupForm />
      
    ) : (title==="RequestPickupConfirmation") ? (
      <RequestPickupConfirmation />
      
    ) : (title==="SelectLocation") ? (
      <SelectLocation />
    ) : (
      <PackageTracking />
    )
  );
};