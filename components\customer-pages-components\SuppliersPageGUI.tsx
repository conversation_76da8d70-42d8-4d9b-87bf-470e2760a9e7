import { Card, YStack, Text, H2, XStack, Paragraph, Input, Image, Button } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, ScrollView } from 'react-native';
import { Stack } from 'expo-router';
import { useRouter } from 'expo-router';

type SuppliersPageGUIProps = {
    category: string;
    search: string;
    setSearch: (value: string) => void;
    filteredSuppliers: Array<{
        id: string;
        name: string;
        logoUrl: string;
        rating: number;
        tags: string[];
    }>;
    filteredPromotions: Array<{
        id: string;
        title: string;
    }>;
};

export const SuppliersPageGUI = ({ category, search, setSearch, filteredSuppliers, filteredPromotions }: SuppliersPageGUIProps) => {
    const router = useRouter();
    return (
        <>
            <Stack.Screen options={{ title: category ? formatTitle(category) : 'Suppliers', headerShown: true }} />
            <ScrollView contentContainerStyle={{ padding: 16, paddingBottom: 40 }}>
            <YStack gap="$4">

            {/* Header */}
            <YStack>
                <H2>{category ? formatTitle(category) : 'Suppliers'}</H2>
                <Paragraph color="$gray10">Find the best {category ? formatTitle(category).toLowerCase() : 'suppliers'} near you</Paragraph>
            </YStack>

            {/* Search + Filter Row */}
            <XStack gap="$2" ai="center" jc="space-between" width="100%">
                <Input
                flex={1}
                placeholder={`Search ${category ? formatTitle(category).toLowerCase() : 'suppliers'}...`}
                value={search}
                onChangeText={setSearch}
                size="$4"
                borderColor="$gray6"
                />
                <Button size="$4" icon={<Ionicons name="filter-outline" size={20} />}>
                Filter
                </Button>
            </XStack>

            {/* Promotions Section */}
            <YStack gap="$2" width="100%">
                <Text fontWeight="bold" fontSize="$5">🔥 Promotions</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <XStack gap="$3">
                    {filteredPromotions.map((promotion) => 
                        <PromoCard key={promotion.id} title={promotion.title} />
                    )}
                </XStack>
                </ScrollView>
            </YStack>

            {/* Supplier List */}
            <YStack gap="$3">
                {filteredSuppliers.map((supplier) => (
                <Pressable
                    key={supplier.id}
                    onPress={() => router.push({
                    pathname: "/home/<USER>",
                    params: { supplierId: supplier.id }
                    })}
                    style={({ pressed, hovered }) => ({
                        transform: pressed ? [{ scale: 0.95 }] : hovered ? [{ scale: 1.02 }] : [{ scale: 1 }]
                    })}
                >
                    <Card
                    pointerEvents='none'
                    p="$3"
                    br="$4"
                    bw="$0.5"
                    boc="$gray5"
                    animation="quick"
                    hoverStyle={{ scale: 0.98 }}
                    pressStyle={{ scale: 0.95 }}
                    >
                    <XStack ai="center" gap="$3">
                        <Image
                        source={{ uri: supplier.logoUrl }}
                        width={60}
                        height={60}
                        borderRadius={12}
                        />
                        <YStack>
                        <Text fontWeight="bold" fontSize="$5">
                            {supplier.name}
                        </Text>
                        <XStack ai="center" gap="$1">
                            <Ionicons name="star" size={14} color="gold" />
                            <Paragraph size="$2">{supplier.rating}</Paragraph>
                            <Paragraph size="$2" color="$gray9">
                            • {supplier.tags.join(', ')}
                            </Paragraph>
                        </XStack>
                        </YStack>
                    </XStack>
                    </Card>
                </Pressable>
                ))}
            </YStack>
            </YStack>
        </ScrollView>
      </>
    );
};

function formatTitle(slug: string | undefined) {
  if (!slug) return ''
  return slug.charAt(0).toUpperCase() + slug.slice(1).replace('-', ' ')
}

function PromoCard({ title }: { title: string }) {
  return (
    <Card
      w={250}
      p="$3"
      br="$5"
      boc="$purple8"
      backgroundColor="#7529B3"
      animation="bouncy"
      hoverStyle={{ scale: 0.97 }}
      pressStyle={{ scale: 0.95 }}
    >
      <Text color="white" fontWeight="bold">{title}</Text>
    </Card>
  )
}