import { ScrollView } from 'react-native';
import { useMyOrdersStore } from './useMyOrdersStore';
import { Text, View, YStack, XStack, Card, Separator, Button } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useState } from 'react';
import { router } from 'expo-router';

export function CustomerOrdersGUI() {
  const orders = useMyOrdersStore((s) => s.orders);
  const [selectedFilter, setSelectedFilter] = useState('All');

  const filteredOrders = selectedFilter === 'All'
    ? orders
    : orders.filter((order) => order.status === selectedFilter);

  const filters = ['All', 'Delivered', 'On the Way', 'Preparing'];

  return (
    <>
    <View
          width={"100%"}
          style={{
              paddingVertical: 40,
              paddingHorizontal: 24,
              borderBottomLeftRadius: 32,
              borderBottomRightRadius: 32,
              backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
              backgroundColor: '#7529B3', // fallback for platforms without gradient support
          }}
          >
          <MotiView
              from={{ opacity: 0, translateY: -20 }}
              animate={{ opacity: 1, translateY: 0 }}
          >
              <Text fontSize="$10" fontWeight="800" color="white" textAlign="center">
              🧾 My Orders
              </Text>
          </MotiView>
      </View>
    <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}>
      
      <YStack p="$4" gap="$4" width={'95%'} alignSelf='center'>
        {/* Filter Bar */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ marginBottom: 16 }}>
          <XStack gap="$2" pl="$4" pr="$2">
            {filters.map((filter) => {
              const isActive = selectedFilter === filter
              return (
                <Button
                  key={filter}
                  size="$3"
                  br="$10"
                  px="$4"
                  py="$2"
                  bg={isActive ? '$primary' : '$color2'}
                  color={isActive ? 'white' : '$gray10'}
                  fontWeight="600"
                  onPress={() => setSelectedFilter(filter)}
                >
                  {filter}
                </Button>
              )
            })}
          </XStack>
        </ScrollView>

        {filteredOrders.length === 0 && (
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing' }}
          >
            <Text mt="$6" fontSize="$5" color="$gray10" textAlign="center">
              You have no orders yet.
            </Text>
          </MotiView>
        )}

        {filteredOrders.map((order, index) => (
          <MotiView
            key={order.id}
            from={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 50 }}
          >
            <Card
              bg="$backgroundStrong"
              br="$6"
              p="$4"
              shadowColor="#000"
              shadowOffset={{ width: 0, height: 4 }}
              shadowOpacity={0.1}
              shadowRadius={6}
              borderColor="$colorTransparent"
            >
              <XStack jc="space-between" ai="center">
                <Text fontWeight="700">#{order.id}</Text>
                <Text color="$gray9" fontSize="$2">
                  {new Date(order.createdAt).toLocaleDateString()} {new Date(order.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Text>
              </XStack>

              <Separator my="$2" />

              <YStack gap="$2" width={'95%'}>
                <XStack gap="$2" ai="center">
                  <Ionicons name="list-outline" size={16} color="#888" />
                  <Text color="$gray10">Items: {order.items.map(i => i.product.name).join(', ')}</Text>
                </XStack>
                <XStack gap="$2" ai="center">
                  <Ionicons name="cash-outline" size={16} color="#888" />
                  <Text color="$gray10">Total: ₪{order.total.toFixed(2)}</Text>
                </XStack>
                <XStack gap="$2" ai="center">
                  <Ionicons name="stats-chart-outline" size={16} color="#888" />
                  <Text color={
                    order.status === 'Delivered' ? '$green10' :
                    order.status === 'On the Way' ? '$orange10' :
                    '$blue10'
                  }>
                    Status: {order.status}
                  </Text>
                </XStack>
              </YStack>

              <Button mt="$3" size="$3" br="$6" variant="outlined" icon={<Ionicons name="eye-outline" size={18} />}
                onPress={() => 
                  router.push({
                    pathname: "/orders/order-details",
                    params: {id: order.id}
                  })
                }
              >
                View Details
              </Button>
            </Card>
          </MotiView>
        ))}
      </YStack>
    </ScrollView>
    </>
  )
}
