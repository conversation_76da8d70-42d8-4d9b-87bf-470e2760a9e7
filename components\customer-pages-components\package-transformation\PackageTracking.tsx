import { useEffect, useState } from 'react';
import { Platform, ScrollView, Dimensions, Linking, TouchableWithoutFeedback } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { View, Text, Button, YStack, XStack } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import MapboxGL from '@rnmapbox/maps';

import { useSendPackageStore } from './useSendPackageStore';
import { useRequestPickupStore } from './useRequestPickupStore';

MapboxGL.setAccessToken('pk.eyJ1Ijoib21hci1qYXJib3UyMDA0IiwiYSI6ImNtY2dlcjF0YTBoNGQybnF2YXczcTZjM3oifQ.bicTczx8tplFAF9fSASLTw');

export function PackageTracking() {
  const { id, type } = useLocalSearchParams<{ id: string; type: 'sent' | 'pickup' }>();
  const router = useRouter();

  const sendRequests = useSendPackageStore((s) => s.sendRequests);
  const pickupRequests = useRequestPickupStore((s) => s.pickupRequests);
  const request = type === 'sent' ? sendRequests[Number(id)] : pickupRequests[Number(id)];

  const pickupLoc = request?.pickup;
  const dropoffLoc = type === 'sent' && 'dropoff' in request ? request?.dropoff : null;

  const customerLocation = {
    latitude: pickupLoc?.lat || 31.95,
    longitude: pickupLoc?.lng || 35.23,
  };

  const [driverLocation, setDriverLocation] = useState({
    latitude: customerLocation.latitude - 0.01,
    longitude: customerLocation.longitude - 0.01,
  });

  useEffect(() => {
    const interval = setInterval(() => {
      setDriverLocation((prev) => {
        const latDiff = customerLocation.latitude - prev.latitude;
        const lonDiff = customerLocation.longitude - prev.longitude;
        const step = 0.0003;

        const newLat = prev.latitude + Math.sign(latDiff) * Math.min(Math.abs(latDiff), step);
        const newLon = prev.longitude + Math.sign(lonDiff) * Math.min(Math.abs(lonDiff), step);

        if (Math.abs(latDiff) < 0.0001 && Math.abs(lonDiff) < 0.0001) {
          clearInterval(interval);
          return prev;
        }

        return { latitude: newLat, longitude: newLon };
      });
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const [eta, setEta] = useState(30 * 60);
  useEffect(() => {
    if (eta <= 0) return;
    const timer = setInterval(() => setEta((e) => e - 1), 1000);
    return () => clearInterval(timer);
  }, [eta]);

  const formatTime = (s: number) => `${Math.floor(s / 60)}m ${s % 60}s`;

  const { height } = Dimensions.get('window');
  const [fullscreen, setFullscreen] = useState(false);

  if (!request) {
    router.replace('/home');
    return null;
  }

  const statuses = ['Preparing', 'On the Way', 'Delivered'];

  return (
    <>
      {fullscreen && (
        <View style={{ position: 'absolute', top: 0, bottom: 0, left: 0, right: 0, zIndex: 999 }}>
          <MapboxGL.MapView style={{ flex: 1 }}>
            <MapboxGL.Camera
              centerCoordinate={[customerLocation.longitude, customerLocation.latitude]}
              zoomLevel={13}
            />
            <MapboxGL.MarkerView coordinate={[customerLocation.longitude, customerLocation.latitude]}>
              <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'green' }} />
            </MapboxGL.MarkerView>
            <MapboxGL.MarkerView coordinate={[driverLocation.longitude, driverLocation.latitude]}>
              <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'purple' }} />
            </MapboxGL.MarkerView>
          </MapboxGL.MapView>

          <Button
            onPress={() => setFullscreen(false)}
            bg="$primary"
            color="white"
            style={{
              position: 'absolute',
              top: 40,
              right: 20,
              zIndex: 1000,
              padding: 10,
            }}
            br={100}
            hoverStyle={{ bg: '$third' }}
            pressStyle={{ bg: '$third' }}
          >
            <Ionicons name="close" size={20} color="#fff" />
          </Button>
        </View>
      )}

      <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 120 }}>
        {!fullscreen && (
          <>
            {/* Header */}
            <View
              style={{
                paddingVertical: 30,
                paddingHorizontal: 24,
                borderBottomLeftRadius: 32,
                borderBottomRightRadius: 32,
                backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
                backgroundColor: '#7529B3',
              }}
            >
              <MotiView from={{ opacity: 0, translateY: -10 }} animate={{ opacity: 1, translateY: 0 }}>
                <Text fontSize="$8" fontWeight="700" color="white" textAlign="center">
                  {type === 'sent' ? '📦 Sent Package' : '📥 Pickup Request'}
                </Text>
              </MotiView>
            </View>

            <YStack p="$4" gap="$4">
              {/* Status Stepper */}
              <XStack justifyContent="space-between" mb="$4" px="$2">
                {statuses.map((status, i) => {
                  const active = i <= 1; // Always show first 2 as active since we have no real status
                  return (
                    <YStack key={status} ai="center">
                      <Ionicons
                        name={
                          status === 'Delivered'
                            ? 'checkmark-circle'
                            : status === 'On the Way'
                            ? 'bicycle'
                            : 'construct-outline'
                        }
                        size={32}
                        color={active ? '#7529B3' : '#ccc'}
                      />
                      <Text mt="$1" color={active ? '#7529B3' : '#ccc'}>
                        {status}
                      </Text>
                    </YStack>
                  );
                })}
              </XStack>

              <Text fontSize="$6">Estimated Arrival: {formatTime(eta)}</Text>

              {'driverName' in request && request.driverName && (
                <XStack justifyContent="space-between">
                  <YStack bg="$gray2" p="$3" br="$6" width="85%">
                    <Text fontWeight="600" fontSize="$5">
                      Driver: {request.driverName}
                    </Text>
                  </YStack>
                  <Button
                    icon={<Ionicons name="call" size={20} color="white" />}
                    onPress={() => Linking.openURL(`tel:${request.driverPhone}`)}
                    bg="$blue10"
                    circular
                    mt="$1"
                  />
                </XStack>
              )}

              <View
                bg="$fourth"
                style={{
                  padding: 10,
                  borderRadius: 10,
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Text fontSize="$5" color="$primary" fontWeight="600">
                  Tap the map to view and move freely
                </Text>
              </View>

              <TouchableWithoutFeedback onPress={() => setFullscreen(true)}>
                <View style={{ height: height * 0.375, borderRadius: 12, overflow: 'hidden' }}>
                  <MapboxGL.MapView style={{ flex: 1 }}>
                    <MapboxGL.Camera
                      centerCoordinate={[customerLocation.longitude, customerLocation.latitude]}
                      zoomLevel={13}
                    />
                    {pickupLoc && (
                      <MapboxGL.MarkerView coordinate={[
                        pickupLoc.lng ?? 0,
                        pickupLoc.lat ?? 0
                      ]}>
                        <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'green' }} />
                      </MapboxGL.MarkerView>
                    )}
                    {dropoffLoc && (
                      <MapboxGL.MarkerView coordinate={[dropoffLoc.lng, dropoffLoc.lat]}>
                        <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'blue' }} />
                      </MapboxGL.MarkerView>
                    )}
                    <MapboxGL.MarkerView coordinate={[driverLocation.longitude, driverLocation.latitude]}>
                      <View style={{ width: 30, height: 30, borderRadius: 15, backgroundColor: 'purple' }} />
                    </MapboxGL.MarkerView>
                  </MapboxGL.MapView>
                </View>
              </TouchableWithoutFeedback>

              <Button
                mt="$4"
                size="$5"
                bg="$primary"
                color="white"
                br="$10"
                onPress={() => router.back()}
                hoverStyle={{ bg: '$third' }}
                pressStyle={{ bg: '$third' }}
              >
                Back
              </Button>
            </YStack>
          </>
        )}
      </ScrollView>
    </>
  );
}
