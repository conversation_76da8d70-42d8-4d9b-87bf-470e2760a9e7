{"expo": {"name": "wasel", "slug": "wasel", "version": "1.0.0", "scheme": "wasel", "web": {"bundler": "metro", "output": "static", "favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-dev-launcher", {"launchMode": "most-recent"}], "expo-font", ["@rnmapbox/maps", {"RNMapboxMapsDownloadToken": "****************************************************************************************************"}], ["expo-location", {"locationWhenInUsePermission": "Show current location on map."}]], "experiments": {"typedRoutes": true, "tsconfigPaths": true}, "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.omarj2004.wasel", "config": {"googleMaps": {"apiKey": "process.env.GOOGLE_MAPS_API_KEY"}}}, "extra": {"router": {}, "eas": {"projectId": "282556d0-7baa-43a2-94e5-28c6c4239267"}}}}