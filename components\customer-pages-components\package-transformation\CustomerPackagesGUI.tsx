import { useState } from 'react';
import { ScrollView } from 'react-native';
import { Button, Text, View, YStack, XStack, Card, Separator } from 'tamagui';
import { MotiView } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { useRequestPickupStore } from './useRequestPickupStore';
import { useSendPackageStore } from './useSendPackageStore';
import { router } from 'expo-router';

export function CustomerPackagesGUI() {
  const [activeTab, setActiveTab] = useState<'sent' | 'pickup'>('sent');
  const { pickupRequests } = useRequestPickupStore();
  const { sendRequests } = useSendPackageStore();

  const data = activeTab === 'sent' ? sendRequests : pickupRequests;

  return (
    <>
      {/* Gradient Header */}
      <View
        width="100%"
        style={{
          paddingVertical: 40,
          paddingHorizontal: 24,
          borderBottomLeftRadius: 32,
          borderBottomRightRadius: 32,
          backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
          backgroundColor: '#7529B3',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -20 }} animate={{ opacity: 1, translateY: 0 }}>
          <Text fontSize="$10" fontWeight="800" color="white" textAlign="center">
            📦 My Packages
          </Text>
        </MotiView>
      </View>

      <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 120 }}>
        {/* Tabs */}
        <XStack jc="center" mt="$3" mb="$2" gap="$2">
          <Button
            size="$3"
            chromeless
            px="$4"
            py="$2"
            br="$10"
            bg={activeTab === 'sent' ? '$primary' : '$gray4'}
            color={activeTab === 'sent' ? 'white' : '$gray10'}
            onPress={() => setActiveTab('sent')}
          >
            Sent Packages
          </Button>
          <Button
            size="$3"
            chromeless
            px="$4"
            py="$2"
            br="$10"
            bg={activeTab === 'pickup' ? '$primary' : '$gray4'}
            color={activeTab === 'pickup' ? 'white' : '$gray10'}
            onPress={() => setActiveTab('pickup')}
          >
            Pickup Requests
          </Button>
        </XStack>

        {/* Cards */}
        <YStack px="$4" pt="$2" gap="$4" width="95%" alignSelf='center'>
          {data.length === 0 ? (
            <MotiView from={{ opacity: 0 }} animate={{ opacity: 1 }}>
              <Text textAlign="center" color="$gray9" fontSize="$6" mt="$6">
                😕 No {activeTab === 'sent' ? 'sent packages' : 'pickup requests'} yet.
              </Text>
            </MotiView>
          ) : (
            data.map((item, index) => (
                <MotiView
                    key={index}
                    from={{ opacity: 0, translateY: 10 }}
                    animate={{ opacity: 1, translateY: 0 }}
                    transition={{ delay: index * 100 }}
                >
                    <Card
                    padded
                    elevate
                    br="$8"
                    bw={1}
                    bc="$gray6"
                    bg="$colorTransparent"
                    style={{
                        overflow: 'hidden',
                        borderWidth: 0,
                        shadowColor: '#000',
                        shadowOpacity: 0.1,
                        shadowRadius: 10,
                        backgroundColor: '#fff',
                    }}
                    >
                    {/* Card Header */}
                    <View
                        px="$4"
                        py="$3"
                        bg="$primary"
                        jc="space-between"
                        ai="center"
                        flexDirection="row"
                        borderTopLeftRadius={12}
                        borderTopRightRadius={12}
                    >
                        <Text color="white" fontWeight="800" fontSize="$6">
                        {activeTab === 'sent' ? '📦 Sent Package' : '📥 Pickup Request'}
                        </Text>
                        <Ionicons name="cube-outline" size={20} color="white" />
                    </View>

                    {/* Card Content */}
                    <YStack p="$4" pb="$0" bg="white" gap="$3" width="95%">
                        {activeTab === 'sent' ? (
                            <>
                            <InfoRow label="From" icon="location-outline" value={item.pickup?.address} />
                            {'dropoff' in item && (
                              <InfoRow label="To" icon="navigate-outline" value={item.dropoff?.address} />
                            )}
                            {'receiverName' in item && 'receiverPhone' in item && (
                              <InfoRow label="Receiver" icon="person-outline" value={`${item.receiverName} (${item.receiverPhone})`} />
                            )}
                            {'packageType' in item && (
                              <InfoRow label="Type" icon="cube-outline" value={item.packageType} />
                            )}
                            <InfoRow label="Notes" icon="document-text-outline" value={item.notes || 'None'} />
                            </>
                        ) : (
                            <>
                            <InfoRow label="Pickup" icon="location-outline" value={item.pickup?.address} />
                            {'itemDescription' in item && (
                              <InfoRow label="Item" icon="pricetag-outline" value={item.itemDescription} />
                            )}
                            {'preferredTime' in item && (
                              <InfoRow label="Time" icon="time-outline" value={item.preferredTime} />
                            )}
                            <InfoRow label="Notes" icon="document-text-outline" value={item.notes || 'None'} />
                            </>
                        )}

                        {'status' in item && item.status && (
                            <InfoRow label="Status" icon="car-outline" value={item.status} />
                        )}
                        {'driverName' in item && (
                            <InfoRow label="Driver" icon="person-circle-outline" value={`${item.driverName} (${item.driverPhone})`} />
                        )}
                        </YStack>
                        <Button mt="$3" size="$3" br="$6" variant="outlined" icon={<Ionicons name="eye-outline" size={18} />}
                          onPress={() => 
                            router.push({
                              pathname: "/packages/package-tracking",
                              params: { id: index.toString(), type: activeTab } // temporarly
                            })
                          }              
                        >
                          Track Package
                        </Button>

                    </Card>
                </MotiView>
                ))
          )}
          <Separator my="$4" />
        </YStack>
      </ScrollView>
    </>
  );
}

function InfoRow({
  label,
  icon,
  value,
}: {
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  value?: string;
}) {
  if (!value) return null;

  return (
    <XStack ai="center" gap="$3">
      <Ionicons name={icon} size={18} color="#888" />
      <Text color="$gray9" fontSize="$4">
        <Text color="$gray11" fontWeight="600">{label}:</Text> {value}
      </Text>
    </XStack>
  );
}
