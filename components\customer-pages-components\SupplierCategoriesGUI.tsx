import { Card, YStack, H2, H4, XS<PERSON>ck, Paragraph, Spacer, Input } from 'tamagui';
import { Ionicons } from '@expo/vector-icons'
import { Pressable, ScrollView } from 'react-native';
import { categories } from '../../temp-data/categories';
import { goTo } from '../goToRoute';

export const SupplierCategoriesGUI = () => {
    return (
        <YStack f={1} bg="$background" padding="$4" paddingTop="$8" gap="$4" width={'100%'} height={'100%'}>
            {/* Label & Location */}
            <YStack gap="$2">
                <H2>Choose a Category</H2>
                <XStack ai="center" gap="$1">
                <Ionicons name="location-outline" size={16} />
                <Paragraph size="$2" color="$gray10">
                    Nablus, Palestine
                </Paragraph>
                </XStack>
            </YStack>

            {/* Search */}
            <Input
                placeholder="Search suppliers…"
                size="$3"
                borderWidth={1}
                bw="$0.5"
            />

            {/* Categories Grid */}
            <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={{ paddingBottom: 40 }}>
                <XStack fw="wrap" jc="space-between">
                {categories.map(({ key, label, icon, color, route }) => (
                    <Pressable
                      key={key}
                      onPress={() => goTo(route)}
                      style={({ pressed, hovered }) => ({
                          width: '48%',
                          transform: pressed ? [{ scale: 0.95 }] : hovered ? [{ scale: 1.02 }] : [{ scale: 1 }]
                      })}
                    >
                      <Card
                        pointerEvents="none"     // 👈 Card no longer blocks the tap
                        p="$3"
                        br="$4"
                        bw="$0.5"
                        boc="$gray5"
                        mb="$3"
                        ai="center"
                        animation="quick"
                        backgroundColor="#8F3DD2"
                      >
                        <Ionicons name={icon as any} size={32} color={color} />
                        <Spacer size="$2" />
                        <H4 ta="center" color="white">
                          {label}
                        </H4>
                      </Card>
                    </Pressable>
                ))}
                </XStack>
            </ScrollView>
        </YStack>
    );
};