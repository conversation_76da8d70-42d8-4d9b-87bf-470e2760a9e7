import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, H6, Separator, Theme } from 'tamagui';
import { CustomTextField } from '../CustomTextField';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form'
import { Button } from '~/components/Button';
import { ScrollView } from 'react-native'
import { Image } from 'react-native';
import { grayDark } from '@tamagui/themes';
import LoginLogo from '../LoginLogo';

type SignupGUIProps = {
    children?: React.ReactNode;
    methods: UseFormReturn<FieldValues, any, FieldValues>;
    onSubmit: () => void;
}

export const SignupGUI = ({ children, methods, onSubmit }: SignupGUIProps) => {
    return (
        <Theme name="light">
            <FormProvider {...methods}>
                <YStack flex={1} alignItems="center" justifyContent="center" gap="$4">
                <Separator />
                <LoginLogo />
                <ScrollView contentContainerStyle={{ flexGrow: 1, height: '100%', width: '95%', alignContent: 'center', alignSelf: 'center' }} showsVerticalScrollIndicator={false} style={{ borderBottomColor: '#7529B3', borderBottomWidth: 3, borderCurve: 'circular', borderRadius: 10 }}>
                    <CustomTextField name="username" icon="person" label="UserName" placeholder="Enter your username" />
                    <CustomTextField name="email" icon="mail" keyboardType='email-address' label="Email" placeholder="Enter your email" />
                    <CustomTextField name="phone" icon="phone-portrait" keyboardType='phone-pad' label="Phone" placeholder="Enter your phone number" />
                    <CustomTextField name="password" icon="lock-closed" label="Password" secureTextEntry placeholder="Enter your password" />
                    <CustomTextField name="confirmPassword" icon="lock-closed" label="Confirm Password" secureTextEntry placeholder="Confirm your password" />
                </ScrollView>
                <XStack alignSelf='center' justifyContent="center" alignItems="center" gap="$2" padding={"2%"} width={'100%'}>
                    <Button width={'95%'} title="Signup" onPress={onSubmit}></Button>
                </XStack>
                <H6 color={grayDark.gray10}>Or Signup with</H6>
                <XStack alignSelf='center' justifyContent="center" alignItems="center" gap="$4" padding={"2%"} width={'45%'}>
                    <Button
                    icon={
                        <Image
                        source={require('../../assets/icons/chrome.png')}
                        style={{ width: 24, height: 24 }}
                        />
                    }
                    title=""
                    padding={7}
                    borderRadius={50}
                    backgroundColor="#FFFFFF"
                    onPress={onSubmit}
                    />

                    <Button
                    icon={
                        <Image
                        source={require('../../assets/icons/facebook.png')}
                        style={{ width: 24, height: 24 }}
                        />
                    }
                    title=""
                    padding={7}
                    borderRadius={50}
                    backgroundColor="#FFFFFF"
                    onPress={onSubmit}
                    />
                </XStack>
                {children}
                </YStack>
                
            </FormProvider>
        </Theme>
    );
};