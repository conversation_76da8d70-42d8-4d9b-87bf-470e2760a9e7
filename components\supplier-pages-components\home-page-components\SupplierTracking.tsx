import MapboxGL from '@rnmapbox/maps';
import { useEffect, useRef, useState } from 'react';
import { ScrollView, Dimensions, Pressable, Modal, Linking } from 'react-native';
import { useLocalSearchParams, Stack } from 'expo-router';
import { Text, YStack, Card, H4, H6, XStack, Spinner, Separator, Button, View } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { useMyOrdersStore } from '~/components/customer-pages-components/orders-page-components/useMyOrdersStore';
import type { FeatureCollection, LineString } from 'geojson';

MapboxGL.setAccessToken('pk.eyJ1Ijoib21hci1qYXJib3UyMDA0IiwiYSI6ImNtY2dlcjF0YTBoNGQybnF2YXczcTZjM3oifQ.bicTczx8tplFAF9fSASLTw');

export default function SupplierTracking() {
  const { orderId } = useLocalSearchParams<{ orderId: string }>();
  const order = useMyOrdersStore((s) => s.orders.find((o) => o.id === orderId));

  const [simulatedDriver, setSimulatedDriver] = useState(order?.driverLocation ?? null);
  const [mapFullscreen, setMapFullscreen] = useState(false);
  const cameraRef = useRef<MapboxGL.Camera>(null);

  const dropoff = order?.address?.lng && order?.address?.lat
    ? [order.address.lng, order.address.lat]
    : [35.22, 31.75];

  const driverCoord: [number, number] =
    simulatedDriver && typeof simulatedDriver.lng === 'number' && typeof simulatedDriver.lat === 'number'
      ? [simulatedDriver.lng, simulatedDriver.lat]
      : (dropoff as [number, number]);

  useEffect(() => {
    if (!simulatedDriver) return;
    const interval = setInterval(() => {
      setSimulatedDriver((prev) =>
        prev ? { lng: prev.lng + 0.00008, lat: prev.lat + 0.00005 } : null
      );
    }, 3000);
    return () => clearInterval(interval);
  }, [simulatedDriver]);

  

  const routeLine: FeatureCollection<LineString> = {
    type: 'FeatureCollection',
    features: [{
      type: 'Feature',
      geometry: { type: 'LineString', coordinates: [driverCoord, dropoff] },
      properties: {},
    }],
  };

  if (!order || !simulatedDriver) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Spinner />
        <Text mt="$2">Tracking in progress...</Text>
      </View>
    );
  }

  const statusColor =
    order.status === 'Preparing'
      ? '$yellow7'
      : order.status === 'On the Way'
      ? '$orange6'
      : order.status === 'Delivered' 
      ? '$green6' 
      : '$red7';

  return (
    <>
      <Stack.Screen options={{ title: `Tracking #${orderId}`, headerShown: true }} />

      {/* Fullscreen Map Modal */}
      <Modal visible={mapFullscreen} animationType="slide">
        <View style={{ flex: 1 }}>
          <MapboxGL.MapView style={{ flex: 1 }}>
            <MapboxGL.Camera
              ref={cameraRef}
              zoomLevel={14}
              centerCoordinate={driverCoord}
              animationMode="flyTo"
              animationDuration={1000}
            />
            <MapboxGL.ShapeSource id="route" shape={routeLine}>
              <MapboxGL.LineLayer
                id="routeLine"
                style={{
                  lineColor: '#3b82f6',
                  lineWidth: 4,
                  lineCap: 'round',
                  lineJoin: 'round',
                }}
              />
            </MapboxGL.ShapeSource>
            <MapboxGL.PointAnnotation id="driver" coordinate={driverCoord}>
              <Ionicons name="car" size={30} color="#007aff" />
            </MapboxGL.PointAnnotation>
            <MapboxGL.PointAnnotation id="dropoff" coordinate={dropoff}>
              <Ionicons name="location" size={30} color="#10b981" />
            </MapboxGL.PointAnnotation>
          </MapboxGL.MapView>
          <Button bg='$primary'
            color='white'
            style={{
              position: 'absolute',
              top: 40,
              right: 20,
              zIndex: 1000,
              padding: 10
            }}
            br={100}
            hoverStyle={{ bg: "$third" }} 
            pressStyle={{ bg: "$third" }} 
            onPress={() => setMapFullscreen(false)}
          >
            <Ionicons name='close' size={20} color='#fff'></Ionicons>
          </Button>
        </View>
      </Modal>

      <ScrollView contentContainerStyle={{ padding: 16, paddingBottom: 120 }}>
        <YStack gap="$5">
          {/* Order Summary */}
          <Card bg="$primary" p="$4" br="$8" elevate>
            <YStack gap="$2">
              <H4 color="white">Tracking Order #{order.id}</H4>
              <Text
                  mt="$2"
                  px="$3"
                  py="$1"
                  fontWeight="bold"
                  color="white"
                  fontSize="$2"
                  bg={statusColor}
                  br="$6"
                  alignSelf="flex-start"
                >
                  {order.status}
                </Text>
              <Text color="white">Driver: {order.driverName ?? 'Unassigned'}</Text>
              <Text color="white">Estimated Time: ~12 mins</Text>
            </YStack>
          </Card>

          <View
                        bg='$fourth'
                        style={{
                          padding: 10,
                          borderRadius: 10,
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
          >
            <Text fontSize="$5" color="$primary" fontWeight="600">
              Tap the map to view and move freely
            </Text>
          </View>

          {/* Tap-to-expand Map */}
          <Pressable onPress={() => setMapFullscreen(true)}>
            <Card elevate br="$6" height={300} overflow="hidden">
              <MapboxGL.MapView style={{ flex: 1 }}>
                <MapboxGL.Camera
                  ref={cameraRef}
                  zoomLevel={14}
                  centerCoordinate={driverCoord}
                  animationMode="flyTo"
                  animationDuration={1000}
                />
                <MapboxGL.ShapeSource id="route2" shape={routeLine}>
                  <MapboxGL.LineLayer
                    id="routeLine2"
                    style={{
                      lineColor: '#3b82f6',
                      lineWidth: 4,
                      lineCap: 'round',
                      lineJoin: 'round',
                    }}
                  />
                </MapboxGL.ShapeSource>
                <MapboxGL.PointAnnotation id="driver2" coordinate={driverCoord}>
                  <Ionicons name="car" size={30} color="#007aff" />
                </MapboxGL.PointAnnotation>
                <MapboxGL.PointAnnotation id="dropoff2" coordinate={dropoff}>
                  <Ionicons name="location" size={30} color="#10b981" />
                </MapboxGL.PointAnnotation>
              </MapboxGL.MapView>
            </Card>
          </Pressable>

          {/* Destination Info */}
          <Card elevate p="$4" br="$6" gap="$3">
            <H6>Delivery Destination</H6>
            <Text>{order.address?.address}</Text>
            <XStack justifyContent='space-between'>
              <YStack
                  br="$6"
                  p="$3"
                  bg="#f3f3f3"
                  borderWidth={1}
                  borderColor="#ddd"
                  width="85%"
                >
              <Text color="$gray10">Customer Phone: {order.phone}</Text>
              </YStack>
              <Button
                circular
                icon={<Ionicons name="call" size={20} color="white" />}
                onPress={() => Linking.openURL(`tel:${order.driverPhone}`)}
                mt="$1"
                bg="$blue10"
                br="$8"
              >
              </Button>
            </XStack>
          </Card>

          {/* Timeline */}
          <Card elevate p="$4" br="$6" gap="$3">
            <H6>Delivery Progress</H6>
            <XStack jc="space-between">
              {['Preparing', 'On the Way', 'Delivered'].map((stage) => (
                <YStack ai="center" key={stage}>
                  <Ionicons
                    name={
                      order.status === stage ||
                      (order.status === 'Delivered' && stage !== 'Delivered')
                        ? 'checkmark-circle'
                        : 'ellipse-outline'
                    }
                    size={24}
                    color={
                      order.status === stage ||
                      (order.status === 'Delivered' && stage !== 'Delivered')
                        ? '#10b981'
                        : '#d1d5db'
                    }
                  />
                  <Text fontSize="$2" mt="$1">{stage}</Text>
                </YStack>
              ))}
            </XStack>
          </Card>
        </YStack>
      </ScrollView>
    </>
  );
}
