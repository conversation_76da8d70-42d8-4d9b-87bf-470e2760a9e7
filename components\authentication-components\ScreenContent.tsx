import { useForm } from 'react-hook-form'
import { LoginGUI } from './LoginGUI';
import { SignupGUI } from './SignupGUI';
import { ForgotPass } from './ForgotPassGUI';
import { VerifyItsYou } from './VerifyItsYouGUI';

type ScreenContentProps = {
  title: string;
  path: string;
  children?: React.ReactNode;
};

export const ScreenContent = ({ title, path, children }: ScreenContentProps) => {
  const methods = useForm();

  const onSubmit = methods.handleSubmit((data) => {
    console.log('Form data:', data);
  })
  return (title==="Login") ? (
    <LoginGUI methods={methods} children={children} />
  ) : (title==="Signup") ? (
    <SignupGUI methods={methods} onSubmit={onSubmit} children={children} />
  ) : (title=="VerifyItsYou1") ? (
    <VerifyItsYou verifyCodePage={false} methods={methods} onSubmit={onSubmit} children={children} />
  ) : (title=="VerifyItsYou2") ? (
    <VerifyItsYou verifyCodePage={true} methods={methods} onSubmit={onSubmit} children={children} />
  ) : (
    <ForgotPass methods={methods} onSubmit={onSubmit} children={children} />
  );
};

