import React from 'react'
import { Input, Text, XStack } from 'tamagui'
import { useController, useFormContext } from 'react-hook-form'
import { Ionicons } from '@expo/vector-icons'

type Props = {
  name: string
  icon?: string
  label?: string
  placeholder?: string
  secureTextEntry?: boolean
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad'
}

export const CustomTextField: React.FC<Props> = ({ name, icon, label, placeholder, secureTextEntry, keyboardType = 'default', }) => {
  const { control } = useFormContext()
  const {
    field: { onChange, onBlur, value },
    fieldState: { error }
  } = useController({ name, control })

  return (
    <XStack gap="$2" alignItems="center" padding="$2" width={'100%'}>
      {icon && (
        <Ionicons name={icon as any} size={24} color="#7529B3" />
      )}
      <Input
        width={'95%'}
        height={50}
        fontSize="$5"
        placeholder={placeholder}
        value={value}
        onChangeText={onChange}
        onBlur={onBlur}
        secureTextEntry={secureTextEntry}
        keyboardType={keyboardType}
        borderColor={error ? 'red' : '$borderColor'}
      />
      {error && <Text color="red" fontSize="$2">{error.message}</Text>}
    </XStack>
  )
}
